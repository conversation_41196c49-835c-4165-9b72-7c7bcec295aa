name: Release Application

on:
    push:
        tags:
            - 'v*'

permissions:
    contents: write

jobs:
    build-and-publish:
        name: Build and Publish with Velopack for ${{ matrix.os }}-${{ matrix.runtime }}
        strategy:
            matrix:
                include:
                    -   os: windows-latest
                        runtime: win-x64
                    # - os: ubuntu-latest
                    #   runtime: linux-x64
        runs-on: ${{ matrix.os }}

        steps:
            -   name: Checkout Repository
                uses: actions/checkout@v4
                with:
                    submodules: recursive
                    fetch-depth: 0

            -   name: Setup .NET SDK
                uses: actions/setup-dotnet@v4
                with:
                    dotnet-version: 9.0.x

            -   name: Install GitVersion
                uses: gittools/actions/gitversion/setup@v3.2.1
                with:
                    versionSpec: '6.x'

            -   name: Determine Version
                id: gitversion
                uses: gittools/actions/gitversion/execute@v3.2.1
                with:
                    useConfigFile: true

            -   name: Install Velopack CLI
                run: dotnet tool install -g vpk

            -   name: Install git-cliff
                run: |
                    cargo install git-cliff

            -   name: Publish Application
                run: |
                    dotnet publish -c Release --self-contained -r ${{ matrix.runtime }} src/Polymerium.App/Polymerium.App.csproj

            -   name: Determine release type
                id: get_version
                run: |
                    version="${{ steps.gitversion.outputs.semVer }}"
                    echo "version=$version" >> $GITHUB_OUTPUT

                    # Check if version contains prerelease tag or metadata
                    if [ "${{ steps.gitversion.outputs.preReleaseTag }}" != "" ] || [[ "$version" == *+* ]]; then
                        echo "is_prerelease=true" >> $GITHUB_OUTPUT
                        echo "release_name=v$version (Preview)" >> $GITHUB_OUTPUT
                    else
                        echo "is_prerelease=false" >> $GITHUB_OUTPUT
                        echo "release_name=v$version" >> $GITHUB_OUTPUT
                    fi
                shell: bash

            -   name: Generate Changelog
                id: changelog
                run: |
                    version="${{ steps.gitversion.outputs.semVer }}"
                    git cliff --latest -o changelog.md
                    echo "changelog_file=changelog.md" >> $GITHUB_OUTPUT
                shell: bash

            -   name: Pack with Velopack
                run: |
                    vpk pack --runtime ${{ matrix.runtime }} --packId Polymerium --packVersion ${{ steps.get_version.outputs.version }} --packDir src/Polymerium.App/bin/Release/net9.0/${{ matrix.runtime }}/publish --releaseNotes changelog.md --mainExe Polymerium.App.exe


            -   name: Upload to GitHub Releases
                env:
                    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                run: |
                    pre_flag=""
                    if [ "${{ steps.get_version.outputs.is_prerelease }}" = "true" ]; then
                        pre_flag="--pre"
                    fi

                    vpk upload github --repoUrl https://github.com/${{ github.repository }} $pre_flag \
                      --releaseName "${{ steps.get_version.outputs.release_name }}" \
                      --tag "${{ github.ref_name }}" \
                      --token ${{ secrets.GITHUB_TOKEN }}
                shell: bash

#            -   name: Create GitHub Release
#                id: create_release
#                uses: actions/create-release@v1
#                with:
#                    tag_name: "${{ github.ref_name }}"
#                    release_name: ${{ steps.get_version.outputs.release_name }}
#                    body_path: changelog.md
#                    draft: true
#                    prerelease: ${{ steps.get_version.outputs.is_prerelease }}
#                env:
#                    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
#    
#            -   name: Upload Velopack Artifacts
#                uses: actions/upload-release-asset@v1
#                with:
#                    upload_url: ${{ steps.create_release.outputs.upload_url }}
#                    asset_path: src/Polymerium.App/bin/Release/net9.0/${{ matrix.runtime }}/publish
#                    asset_name: Polymerium-${{ matrix.runtime }}.zip
#                    asset_content_type: application/zip
#                env:
#                    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}