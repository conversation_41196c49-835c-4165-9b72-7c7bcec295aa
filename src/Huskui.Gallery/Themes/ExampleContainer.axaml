<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">

    <ControlTheme x:Key="{x:Type controls:ExampleContainer}" TargetType="controls:ExampleContainer">
        <Setter Property="Background" Value="{StaticResource CardBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
        <Setter Property="Padding" Value="24" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Grid RowDefinitions="Auto,Auto,*,Auto">
                        <!-- Header -->
                        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="16">
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{TemplateBinding Title}"
                                           FontSize="{StaticResource LargeFontSize}"
                                           FontWeight="{StaticResource ControlStrongFontWeight}"
                                           IsVisible="{Binding Title, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                                <TextBlock Text="{TemplateBinding Description}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           TextWrapping="Wrap"
                                           Margin="0,4,0,0"
                                           IsVisible="{Binding Description, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="8">
                                <!-- Options (for custom buttons like ShowCode) -->
                                <ContentPresenter Content="{TemplateBinding Options}"
                                                  IsVisible="{TemplateBinding Options, Converter={x:Static ObjectConverters.IsNotNull}}" />

                                <!-- Enable Toggle Button -->
                                <ToggleButton Name="PART_EnableToggle"
                                              Theme="{StaticResource GhostToggleButtonTheme}"
                                              VerticalAlignment="Center"
                                              ToolTip.Tip="Toggle Enable State">
                                    <fi:SymbolIcon Symbol="Power" FontSize="{StaticResource SmallFontSize}" />
                                </ToggleButton>

                                <!-- Code Toggle Button -->
                                <ToggleButton Name="PART_CodeToggle"
                                              Theme="{StaticResource GhostToggleButtonTheme}"
                                              VerticalAlignment="Center"
                                              ToolTip.Tip="Toggle Code View"
                                              IsChecked="{Binding ShowCode, RelativeSource={RelativeSource AncestorType=controls:ExampleContainer}}"
                                              IsVisible="{Binding XamlCode, RelativeSource={RelativeSource AncestorType=controls:ExampleContainer}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                    <fi:SymbolIcon Symbol="Code" FontSize="{StaticResource SmallFontSize}" />
                                </ToggleButton>
                            </StackPanel>
                        </Grid>

                        <!-- Divider -->
                        <husk:Divider Grid.Row="1" />

                        <!-- Content with Controls Panel -->
                        <Grid Grid.Row="2" ColumnDefinitions="*,Auto">
                            <!-- Main Content -->
                            <ContentPresenter Grid.Column="0"
                                              Name="PART_ContentContainer"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Padding="{TemplateBinding Padding}" />

                            <!-- Controls Panel (Right Side) -->
                            <Border Grid.Column="1"
                                    Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                    BorderBrush="{StaticResource ControlBorderBrush}"
                                    BorderThickness="1,0,0,0"
                                    Padding="16"
                                    MinWidth="200"
                                    IsVisible="{TemplateBinding Controls, Converter={x:Static ObjectConverters.IsNotNull}}">
                                <ContentPresenter Content="{TemplateBinding Controls}" />
                            </Border>
                        </Grid>

                        <!-- Code Section -->
                        <Border Grid.Row="3"
                                Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                BorderBrush="{StaticResource ControlBorderBrush}"
                                BorderThickness="1"
                                Margin="0,16,0,0"
                                IsVisible="{TemplateBinding ShowCode}">
                            <StackPanel>
                                <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                        BorderBrush="{StaticResource ControlBorderBrush}"
                                        BorderThickness="0,0,0,1"
                                        Padding="12,8"
                                        IsVisible="{Binding XamlCode, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                    <TextBlock Text="XAML Code"
                                               FontWeight="{StaticResource ControlStrongFontWeight}"
                                               FontSize="{StaticResource SmallFontSize}" />
                                </Border>
                                <controls:CodeViewer Code="{TemplateBinding XamlCode}"
                                                     Language="xml"
                                                     IsVisible="{Binding XamlCode, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />

                                <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                        BorderBrush="{StaticResource ControlBorderBrush}"
                                        BorderThickness="0,1,0,1"
                                        Padding="12,8"
                                        IsVisible="{Binding CSharpCode, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}">
                                    <TextBlock Text="C# Code"
                                               FontWeight="{StaticResource ControlStrongFontWeight}"
                                               FontSize="{StaticResource SmallFontSize}" />
                                </Border>
                                <controls:CodeViewer Code="{TemplateBinding CSharpCode}"
                                                     Language="csharp"
                                                     CornerRadius="{TemplateBinding CornerRadius,Converter={x:Static husk:CornerRadiusConverters.Lower}}"
                                                     IsVisible="{Binding CSharpCode, RelativeSource={RelativeSource TemplatedParent}, Converter={x:Static StringConverters.IsNotNullOrEmpty}}" />
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

</ResourceDictionary>