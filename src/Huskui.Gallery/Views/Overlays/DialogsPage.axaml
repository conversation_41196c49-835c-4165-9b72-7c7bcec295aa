<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
             xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Huskui.Gallery.Views.Overlays.DialogsPage">

    <ScrollViewer>
        <StackPanel Margin="24" Spacing="32">

            <controls:ExampleContainer Title="Confirmation Dialogs"
                                       Description="Binary choice dialogs for user confirmation">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Confirmation Controls" FontWeight="Bold" FontSize="14" />
                        <Button Content="Delete Confirmation" Click="OnShowDeleteConfirmClick" />
                        <Button Content="Save Changes" Click="OnShowSaveConfirmClick" />
                        <Button Content="Email Input" Click="OnShowEmailInputClick" />
                        <Button Content="Exit Application" Click="OnShowExitConfirmClick" />
                        <TextBlock Text="Click buttons to show confirmation dialogs with Primary/Secondary commands."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>

                <StackPanel Spacing="16" Width="400">
                    <husk:Card Padding="16">
                        <StackPanel Spacing="12">
                            <TextBlock Text="Dialog Purpose" FontSize="16" FontWeight="Bold" />
                            <TextBlock
                                Text="Dialogs are designed for pure user input collection with binary choices. They only have PrimaryCommand (positive/affirmative) and SecondaryCommand (negative/cancel) actions."
                                TextWrapping="Wrap" />
                            <TextBlock
                                Text="Use dialogs when you need a clear yes/no, confirm/cancel, or save/discard decision from the user."
                                TextWrapping="Wrap"
                                Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                    </husk:Card>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Input Dialogs"
                                       Description="Dialogs for collecting simple user input">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Input Collection" FontWeight="Bold" FontSize="14" />
                        <Button Content="Rename File" Click="OnShowRenameDialogClick" />
                        <Button Content="Create Folder" Click="OnShowCreateFolderClick" />
                        <Button Content="Set Password" Click="OnShowPasswordDialogClick" />
                        <TextBlock Text="These dialogs collect simple input with clear accept/cancel actions."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>

                <StackPanel Spacing="16" Width="450">
                    <husk:Card Padding="16">
                        <StackPanel Spacing="12">
                            <TextBlock Text="Input Patterns" FontSize="16" FontWeight="Bold" />
                            <StackPanel Spacing="8">
                                <TextBlock Text="• Single text input (filename, folder name)" />
                                <TextBlock Text="• Password or sensitive data entry" />
                                <TextBlock Text="• Simple configuration values" />
                                <TextBlock Text="• Quick data collection with validation" />
                            </StackPanel>
                        </StackPanel>
                    </husk:Card>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Decision Dialogs"
                                       Description="Critical decision points requiring user choice">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Decision Points" FontWeight="Bold" FontSize="14" />
                        <Button Content="Unsaved Changes" Click="OnShowUnsavedChangesClick" />
                        <Button Content="Overwrite File" Click="OnShowOverwriteDialogClick" />
                        <Button Content="Permanent Action" Click="OnShowPermanentActionClick" />
                        <TextBlock Text="Critical decisions that require explicit user confirmation."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>

                <StackPanel Spacing="16" Width="500">
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="16">
                        <husk:Card Grid.Column="0" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="⚠️ Destructive Actions" FontWeight="Bold" />
                                <TextBlock Text="Delete, overwrite, or permanent changes"
                                           FontSize="12"
                                           TextWrapping="Wrap" />
                            </StackPanel>
                        </husk:Card>

                        <husk:Card Grid.Column="1" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="💾 Data Loss Prevention" FontWeight="Bold" />
                                <TextBlock Text="Save/discard unsaved changes"
                                           FontSize="12"
                                           TextWrapping="Wrap" />
                            </StackPanel>
                        </husk:Card>
                    </Grid>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Dialog Result Validation"
                                       Description="Dialog with Result property binding and validation"
                                       XamlCode="&lt;husk:Dialog xmlns=&quot;https://github.com/avaloniaui&quot;&#xA;             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2006/xaml&quot;&#xA;             xmlns:husk=&quot;https://github.com/d3ara1n/Huskui.Avalonia&quot;&#xA;             x:Class=&quot;Huskui.Gallery.Dialogs.EmailInputDialog&quot;&#xA;             Title=&quot;Enter Email Address&quot;&#xA;             PrimaryText=&quot;Confirm&quot;&#xA;             SecondaryText=&quot;Cancel&quot;&#xA;             IsPrimaryButtonVisible=&quot;True&quot;&gt;&#xA;&#xA;    &lt;StackPanel Spacing=&quot;16&quot; Width=&quot;350&quot; Margin=&quot;24&quot;&gt;&#xA;        &lt;StackPanel Spacing=&quot;8&quot;&gt;&#xA;            &lt;TextBlock Text=&quot;Please enter your email address:&quot; FontWeight=&quot;Medium&quot; /&gt;&#xA;            &lt;TextBox x:Name=&quot;EmailTextBox&quot;&#xA;                     Text=&quot;{Binding Result, RelativeSource={RelativeSource AncestorType=husk:Dialog}}&quot;&#xA;                     Watermark=&quot;<EMAIL>&quot;&#xA;                     KeyDown=&quot;OnEmailTextBoxKeyDown&quot; /&gt;&#xA;        &lt;/StackPanel&gt;&#xA;        &lt;husk:InfoBar x:Name=&quot;ValidationBorder&quot; IsVisible=&quot;False&quot; Classes=&quot;Danger&quot;&gt;&#xA;            &lt;TextBlock x:Name=&quot;ValidationText&quot; Text=&quot;Please enter a valid email address&quot; FontSize=&quot;12&quot; /&gt;&#xA;        &lt;/husk:InfoBar&gt;&#xA;    &lt;/StackPanel&gt;&#xA;&lt;/husk:Dialog&gt;"
                                       CSharpCode="using System.Text.RegularExpressions;&#xA;using Avalonia.Controls;&#xA;using Avalonia.Input;&#xA;using Avalonia.Threading;&#xA;using Huskui.Avalonia.Controls;&#xA;&#xA;namespace Huskui.Gallery.Dialogs;&#xA;&#xA;public partial class EmailInputDialog : Dialog&#xA;{&#xA;    private static readonly Regex EmailRegex = new(@&quot;^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$&quot;,&#xA;                                                   RegexOptions.Compiled);&#xA;&#xA;    public EmailInputDialog()&#xA;    {&#xA;        InitializeComponent();&#xA;        UpdateValidation();&#xA;    }&#xA;&#xA;    protected override bool ValidateResult(object? result)&#xA;    {&#xA;        var email = result as string;&#xA;        var isValid = !string.IsNullOrWhiteSpace(email) &amp;&amp; EmailRegex.IsMatch(email);&#xA;        UpdateValidationUI(isValid, email);&#xA;        return isValid;&#xA;    }&#xA;&#xA;    private void UpdateValidation()&#xA;    {&#xA;        ValidateResult(Result);&#xA;    }&#xA;&#xA;    private void UpdateValidationUI(bool isValid, string? email)&#xA;    {&#xA;        var validationBorder = this.FindControl&lt;InfoBar&gt;(&quot;ValidationBorder&quot;);&#xA;        var validationText = this.FindControl&lt;TextBlock&gt;(&quot;ValidationText&quot;);&#xA;&#xA;        if (validationBorder == null || validationText == null) return;&#xA;&#xA;        if (string.IsNullOrWhiteSpace(email))&#xA;        {&#xA;            validationBorder.IsVisible = false;&#xA;        }&#xA;        else if (!isValid)&#xA;        {&#xA;            validationBorder.IsVisible = true;&#xA;            validationText.Text = &quot;Please enter a valid email address (e.g., <EMAIL>)&quot;;&#xA;        }&#xA;        else&#xA;        {&#xA;            validationBorder.IsVisible = false;&#xA;        }&#xA;    }&#xA;&#xA;    private void OnEmailTextBoxKeyDown(object? sender, KeyEventArgs e)&#xA;    {&#xA;        Dispatcher.UIThread.Post(UpdateValidation);&#xA;        if (e.Key == Key.Enter &amp;&amp; ValidateResult(Result))&#xA;        {&#xA;            Dismiss();&#xA;        }&#xA;    }&#xA;&#xA;    // Usage example:&#xA;    // var dialog = new EmailInputDialog();&#xA;    // appWindow.PopDialog(dialog);&#xA;    // if (await dialog.CompletionSource.Task)&#xA;    // {&#xA;    //     var email = dialog.Result as string;&#xA;    //     // Use the validated email&#xA;    // }&#xA;}" >
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Result Validation Example" FontWeight="Bold" FontSize="14" />
                        <Button Content="Email Input Dialog" Click="OnShowEmailInputClick" />
                        <TextBlock Text="This dialog demonstrates Result property binding, ValidateResult override, and CompletionSource usage."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>

                <StackPanel Spacing="16" Width="500">
                    <husk:Card Padding="16">
                        <StackPanel Spacing="12">
                            <TextBlock Text="Dialog Result Validation 概述" FontSize="16" FontWeight="Bold" />

                            <TextBlock Text="Dialog使用Result属性与前端绑定作为数据校验和返回值的中间媒介，并用重写ValidateResult方法的方式确定结果有效性以及是否可以按下Primary按钮以接受这个结果返回给外部。"
                                       TextWrapping="Wrap"
                                       FontSize="13" />

                            <StackPanel Spacing="8">
                                <TextBlock Text="核心特性：" FontWeight="Medium" FontSize="12" />
                                <StackPanel Spacing="4" Margin="12,0,0,0">
                                    <TextBlock Text="• Result属性：双向绑定用户输入数据" FontSize="12" />
                                    <TextBlock Text="• ValidateResult方法：重写以实现自定义验证逻辑" FontSize="12" />
                                    <TextBlock Text="• Primary按钮状态：根据验证结果自动启用/禁用" FontSize="12" />
                                    <TextBlock Text="• CompletionSource：异步等待用户操作结果" FontSize="12" />
                                </StackPanel>
                            </StackPanel>

                            <StackPanel Spacing="8">
                                <TextBlock Text="工作流程：" FontWeight="Medium" FontSize="12" />
                                <StackPanel Spacing="4" Margin="12,0,0,0">
                                    <TextBlock Text="1. 用户在输入控件中输入数据" FontSize="12" />
                                    <TextBlock Text="2. 数据通过绑定更新到Dialog.Result属性" FontSize="12" />
                                    <TextBlock Text="3. ValidateResult方法自动调用验证数据" FontSize="12" />
                                    <TextBlock Text="4. Primary按钮根据验证结果启用或禁用" FontSize="12" />
                                    <TextBlock Text="5. 用户确认后通过CompletionSource返回结果" FontSize="12" />
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </husk:Card>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Dialog Best Practices"
                                       Description="Guidelines for effective dialog usage">
                <StackPanel Spacing="16" Width="600">
                    <husk:Card Padding="20">
                        <StackPanel Spacing="16">
                            <TextBlock Text="Design Guidelines" FontSize="18" FontWeight="Bold" />

                            <StackPanel Spacing="12">
                                <StackPanel Spacing="4">
                                    <TextBlock Text="✅ Do" FontWeight="Bold" Foreground="Green" />
                                    <TextBlock Text="• Use clear, actionable button labels (Save/Cancel, Delete/Keep)" />
                                    <TextBlock Text="• Keep content focused on the single decision" />
                                    <TextBlock Text="• Make the primary action obvious and safe" />
                                    <TextBlock Text="• Provide clear context for the decision" />
                                </StackPanel>

                                <husk:Divider />

                                <StackPanel Spacing="4">
                                    <TextBlock Text="❌ Don't" FontWeight="Bold" Foreground="Red" />
                                    <TextBlock Text="• Use for complex interactions (use Modal instead)" />
                                    <TextBlock Text="• Add more than two action buttons" />
                                    <TextBlock Text="• Use vague labels like 'OK' or 'Yes'" />
                                    <TextBlock Text="• Include complex forms or multiple inputs" />
                                </StackPanel>
                            </StackPanel>

                            <husk:Divider />

                            <StackPanel Spacing="8">
                                <TextBlock Text="Technical Notes" FontWeight="Bold" />
                                <TextBlock
                                    Text="Dialogs are designed for binary decisions with PrimaryCommand (affirmative) and SecondaryCommand (negative/cancel). They focus user attention on a single decision point and should be used sparingly for critical choices."
                                    TextWrapping="Wrap"
                                    FontSize="12"
                                    Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            </StackPanel>
                        </StackPanel>
                    </husk:Card>
                </StackPanel>
            </controls:ExampleContainer>

        </StackPanel>
    </ScrollViewer>

</UserControl>