<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
             x:Class="Huskui.Gallery.Views.Pages.HomePage">

    <ScrollViewer Padding="48">
        <StackPanel Spacing="48" MaxWidth="800">
            <!-- Welcome Section -->
            <StackPanel Spacing="16">
                <TextBlock Text="Welcome to Huskui Gallery"
                           Classes="WelcomeTitle" />
                <TextBlock Text="Explore the comprehensive collection of Huskui.Avalonia controls and components"
                           Classes="WelcomeSubtitle"
                           TextWrapping="Wrap" />
            </StackPanel>

            <!-- Features Grid -->
            <Grid RowDefinitions="Auto,*" ColumnDefinitions="*,*" RowSpacing="24" ColumnSpacing="24">
                <TextBlock Grid.Row="0" Grid.ColumnSpan="2"
                           Text="Features"
                           FontSize="{StaticResource LargeFontSize}"
                           FontWeight="{StaticResource ControlStrongFontWeight}"
                           Margin="0,0,0,16" />

                <!-- Rich Components -->
                <Border Grid.Row="1" Grid.Column="0" Classes="FeatureCard">
                    <StackPanel Spacing="16">
                        <fi:SymbolIcon Symbol="ControlButton"
                                       FontSize="32"
                                       Foreground="{StaticResource ControlAccentForegroundBrush}" />
                        <StackPanel Spacing="8">
                            <TextBlock Text="Rich Component Library"
                                       FontSize="{StaticResource LargeFontSize}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock Text="Comprehensive set of UI components including buttons, cards, info bars, and more"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       TextWrapping="Wrap" />
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Theming Support -->
                <Border Grid.Row="1" Grid.Column="1" Classes="FeatureCard">
                    <StackPanel Spacing="16">
                        <fi:SymbolIcon Symbol="ColorBackground"
                                       FontSize="32"
                                       Foreground="{StaticResource ControlAccentForegroundBrush}" />
                        <StackPanel Spacing="8">
                            <TextBlock Text="Advanced Theming"
                                       FontSize="{StaticResource LargeFontSize}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock Text="Built-in support for light/dark themes with customizable accent colors and corner styles"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       TextWrapping="Wrap" />
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Start -->
            <StackPanel Spacing="24">
                <TextBlock Text="Quick Start"
                           FontSize="{StaticResource LargeFontSize}"
                           FontWeight="{StaticResource ControlStrongFontWeight}" />

                <husk:Card Padding="24">
                    <StackPanel Spacing="16">
                        <TextBlock Text="Getting Started with Huskui.Avalonia"
                                   FontWeight="{StaticResource ControlStrongFontWeight}" />
                        
                        <TextBlock Text="1. Install the NuGet package:"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        
                        <Border Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                BorderBrush="{StaticResource ControlBorderBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="12">
                            <TextBlock Text="dotnet add package Huskui.Avalonia"
                                       FontFamily="Consolas,Monaco,Menlo,monospace"
                                       FontSize="{StaticResource SmallFontSize}" />
                        </Border>

                        <TextBlock Text="2. Add the theme to your App.axaml:"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />

                        <Border Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                BorderBrush="{StaticResource ControlBorderBrush}"
                                BorderThickness="1"
                                CornerRadius="{StaticResource SmallCornerRadius}"
                                Padding="12">
                            <TextBlock FontFamily="Consolas,Monaco,Menlo,monospace"
                                       FontSize="{StaticResource SmallFontSize}">
                                <Run Text="&lt;Application.Styles&gt;" />
                                <LineBreak />
                                <Run Text="    &lt;husk:HuskuiTheme Accent=&quot;Blue&quot; /&gt;" />
                                <LineBreak />
                                <Run Text="&lt;/Application.Styles&gt;" />
                            </TextBlock>
                        </Border>

                        <TextBlock Text="3. Start using Huskui controls in your views!"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </husk:Card>
            </StackPanel>

            <!-- Navigation Help -->
            <StackPanel Spacing="16">
                <TextBlock Text="Navigation"
                           FontSize="{StaticResource LargeFontSize}"
                           FontWeight="{StaticResource ControlStrongFontWeight}" />
                
                <husk:InfoBar Header="Explore the Gallery">
                    <TextBlock TextWrapping="Wrap">
                        Use the sidebar to browse different categories of controls. 
                        Each control page includes live examples, code snippets, and customization options.
                        Use the search box to quickly find specific controls.
                    </TextBlock>
                </husk:InfoBar>
            </StackPanel>

            <!-- Footer -->
            <StackPanel Spacing="16">
                <husk:Divider />
                <Grid ColumnDefinitions="*,Auto">
                    <StackPanel Grid.Column="0" Spacing="4">
                        <TextBlock Text="Huskui.Avalonia Gallery"
                                   FontWeight="{StaticResource ControlStrongFontWeight}" />
                        <TextBlock Text="A modern UI component library for Avalonia"
                                   FontSize="{StaticResource SmallFontSize}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                    <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="8">
                        <Button Theme="{StaticResource GhostButtonTheme}"
                                ToolTip.Tip="GitHub Repository">
                            <fi:SymbolIcon Symbol="Open" FontSize="{StaticResource MediumFontSize}" />
                        </Button>
                        <Button Theme="{StaticResource GhostButtonTheme}"
                                ToolTip.Tip="Documentation">
                            <fi:SymbolIcon Symbol="Document" FontSize="{StaticResource MediumFontSize}" />
                        </Button>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>

</UserControl>
