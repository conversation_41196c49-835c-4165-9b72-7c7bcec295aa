<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Huskui.Gallery.Views.Input.ComboBoxesPage">

    <ScrollViewer>
        <StackPanel Margin="24" Spacing="32">

            <controls:ExampleContainer Title="Basic ComboBoxes"
                                       Description="Dropdown selection controls with different configurations">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Selection Controls" FontWeight="Bold" FontSize="14" />
                        <Button Content="Clear Selection" Click="OnClearSelectionClick" />
                        <Button Content="Reset to Defaults" Click="OnResetSelectionClick" />
                        <TextBlock Text="Clear all selections or reset to default values."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>
                
                <StackPanel Spacing="16" Width="300">
                    <StackPanel Spacing="8">
                        <TextBlock Text="Countries" />
                        <ComboBox x:Name="CountryCombo"
                                  SelectedIndex="0">
                            <ComboBoxItem Content="United States" />
                            <ComboBoxItem Content="Canada" />
                            <ComboBoxItem Content="United Kingdom" />
                            <ComboBoxItem Content="Germany" />
                            <ComboBoxItem Content="France" />
                            <ComboBoxItem Content="Japan" />
                            <ComboBoxItem Content="Australia" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="Programming Languages" />
                        <ComboBox x:Name="LanguageCombo"
                                  SelectedIndex="2">
                            <ComboBoxItem Content="C#" />
                            <ComboBoxItem Content="JavaScript" />
                            <ComboBoxItem Content="Python" />
                            <ComboBoxItem Content="Java" />
                            <ComboBoxItem Content="TypeScript" />
                            <ComboBoxItem Content="Rust" />
                            <ComboBoxItem Content="Go" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="With Watermark" />
                        <ComboBox x:Name="WatermarkCombo"
                                  PlaceholderText="Select an option...">
                            <ComboBoxItem Content="Option 1" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="ComboBox Styles"
                                       Description="Different visual styles for dropdown controls">
                <StackPanel Spacing="16" Width="300">
                    <StackPanel Spacing="8">
                        <TextBlock Text="Default Style" />
                        <ComboBox SelectedIndex="0">
                            <ComboBoxItem Content="Default ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="Ghost Style" />
                        <ComboBox Theme="{StaticResource GhostComboBoxTheme}" SelectedIndex="0">
                            <ComboBoxItem Content="Ghost ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Spacing="8">
                        <TextBlock Text="Primary Style" />
                        <ComboBox Classes="Primary" SelectedIndex="0">
                            <ComboBoxItem Content="Primary ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="Success Style" />
                        <ComboBox Classes="Success" SelectedIndex="0">
                            <ComboBoxItem Content="Success ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="Warning Style" />
                        <ComboBox Classes="Warning" SelectedIndex="0">
                            <ComboBoxItem Content="Warning ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="Error Style" />
                        <ComboBox Classes="Error" SelectedIndex="0">
                            <ComboBoxItem Content="Error ComboBox" />
                            <ComboBoxItem Content="Option 2" />
                            <ComboBoxItem Content="Option 3" />
                        </ComboBox>
                    </StackPanel>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Settings Form Example"
                                       Description="ComboBoxes in a practical settings interface">
                <husk:Card Padding="24" Width="400">
                    <StackPanel Spacing="20">
                        <TextBlock Text="Application Settings" FontSize="18" FontWeight="Bold" />
                        
                        <husk:Divider />
                        
                        <StackPanel Spacing="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="Theme" />
                                <ComboBox SelectedIndex="0">
                                    <ComboBoxItem Content="System Default" />
                                    <ComboBoxItem Content="Light" />
                                    <ComboBoxItem Content="Dark" />
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Language" />
                                <ComboBox SelectedIndex="0">
                                    <ComboBoxItem Content="English" />
                                    <ComboBoxItem Content="中文" />
                                    <ComboBoxItem Content="Español" />
                                    <ComboBoxItem Content="Français" />
                                    <ComboBoxItem Content="Deutsch" />
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Font Size" />
                                <ComboBox SelectedIndex="1">
                                    <ComboBoxItem Content="Small" />
                                    <ComboBoxItem Content="Medium" />
                                    <ComboBoxItem Content="Large" />
                                    <ComboBoxItem Content="Extra Large" />
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Auto-save Interval" />
                                <ComboBox SelectedIndex="2">
                                    <ComboBoxItem Content="Never" />
                                    <ComboBoxItem Content="1 minute" />
                                    <ComboBoxItem Content="5 minutes" />
                                    <ComboBoxItem Content="10 minutes" />
                                    <ComboBoxItem Content="30 minutes" />
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                        
                        <husk:Divider />
                        
                        <StackPanel Orientation="Horizontal" Spacing="12" HorizontalAlignment="Right">
                            <Button Content="Reset to Defaults" />
                            <Button Content="Apply Settings" Classes="Primary" />
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </controls:ExampleContainer>

        </StackPanel>
    </ScrollViewer>

</UserControl>
