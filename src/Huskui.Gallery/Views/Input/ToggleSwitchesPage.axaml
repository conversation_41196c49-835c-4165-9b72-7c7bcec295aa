<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Huskui.Gallery.Views.Input.ToggleSwitchesPage">

    <ScrollViewer>
        <StackPanel Margin="24" Spacing="32">

            <controls:ExampleContainer Title="Basic Toggle Switches"
                                       Description="Standard toggle switches with different states">

                <StackPanel Spacing="16">
                    <StackPanel Spacing="12">
                        <ToggleSwitch Content="Default Toggle" />
                        <ToggleSwitch Content="Checked Toggle" IsChecked="True" />
                        <ToggleSwitch Content="Unchecked Toggle" IsChecked="False" />
                    </StackPanel>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Toggle Switch Styles"
                                       Description="Different visual styles for toggle switches">

                <StackPanel Spacing="16">
                    <StackPanel Spacing="12">
                        <ToggleSwitch Content="Default Style" />
                        <ToggleSwitch Content="Primary Style" Classes="Primary" />
                        <ToggleSwitch Content="Success Style" Classes="Success" />
                        <ToggleSwitch Content="Warning Style" Classes="Warning" />
                        <ToggleSwitch Content="Danger Style" Classes="Danger" />
                    </StackPanel>
                </StackPanel>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Settings Panel Example"
                                       Description="Real-world usage in settings panels">
                <husk:Card Padding="24" Width="400">
                    <StackPanel Spacing="20">
                        <TextBlock Text="Application Settings" FontSize="18" FontWeight="Bold" />
                        
                        <husk:Divider />
                        
                        <StackPanel Spacing="16">
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Enable notifications" IsChecked="True" />
                                <TextBlock Text="Receive push notifications for important updates" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Auto-save documents" IsChecked="True" />
                                <TextBlock Text="Automatically save changes every 5 minutes" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Dark mode" IsChecked="False" />
                                <TextBlock Text="Use dark theme for better viewing in low light" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Analytics" IsChecked="False" />
                                <TextBlock Text="Help improve the app by sharing usage data" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                        </StackPanel>
                        
                        <husk:Divider />
                        
                        <StackPanel Orientation="Horizontal" Spacing="12" HorizontalAlignment="Right">
                            <Button Content="Reset to Defaults" />
                            <Button Content="Save Settings" Classes="Primary" />
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Feature Toggles"
                                       Description="Feature flags and experimental options">
                <husk:Card Padding="24" Width="450">
                    <StackPanel Spacing="20">
                        <StackPanel Spacing="8">
                            <TextBlock Text="Experimental Features" FontSize="18" FontWeight="Bold" />
                            <TextBlock Text="Enable these features at your own risk. They may be unstable." 
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </StackPanel>
                        
                        <husk:Divider />
                        
                        <StackPanel Spacing="16">
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="New UI Framework" 
                                                   Classes="Primary"
                                                   IsChecked="False" />
                                <TextBlock Text="🧪 Experimental new user interface components" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Advanced Search" 
                                                   Classes="Success"
                                                   IsChecked="True" />
                                <TextBlock Text="✨ Enhanced search with AI-powered suggestions" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Beta Performance Mode" 
                                                   Classes="Warning"
                                                   IsChecked="False" />
                                <TextBlock Text="⚡ Experimental performance optimizations" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <ToggleSwitch Content="Debug Mode" 
                                                   Classes="Danger"
                                                   IsChecked="False" />
                                <TextBlock Text="🐛 Show detailed error information and logs" 
                                           FontSize="12" 
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" 
                                           Margin="32,0,0,0" />
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </controls:ExampleContainer>

        </StackPanel>
    </ScrollViewer>

</UserControl>
