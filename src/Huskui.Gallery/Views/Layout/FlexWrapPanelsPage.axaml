<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Huskui.Gallery.Views.Layout.FlexWrapPanelsPage">

    <ScrollViewer>
        <StackPanel Margin="24" Spacing="32">

            <controls:ExampleContainer Title="Basic FlexWrapPanel"
                                       Description="Flexible wrapping layout that distributes width evenly">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Spacing Controls" FontWeight="Bold" FontSize="14" />
                        <StackPanel Spacing="8">
                            <TextBlock Text="Column Spacing" FontSize="12" />
                            <Slider x:Name="ColumnSpacingSlider" 
                                    Minimum="0" Maximum="20" Value="8" 
                                    TickFrequency="2" IsSnapToTickEnabled="True" />
                        </StackPanel>
                        <StackPanel Spacing="8">
                            <TextBlock Text="Row Spacing" FontSize="12" />
                            <Slider x:Name="RowSpacingSlider" 
                                    Minimum="0" Maximum="20" Value="8" 
                                    TickFrequency="2" IsSnapToTickEnabled="True" />
                        </StackPanel>
                        <TextBlock Text="Adjust spacing to see how FlexWrapPanel distributes elements." 
                                   TextWrapping="Wrap" 
                                   FontSize="12" 
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>
                
                <Border BorderBrush="{StaticResource ControlBorderBrush}" 
                        BorderThickness="1" 
                        CornerRadius="8" 
                        Padding="16"
                        Width="500">
                    <husk:FlexWrapPanel ColumnSpacing="{Binding #ColumnSpacingSlider.Value}"
                                        RowSpacing="{Binding #RowSpacingSlider.Value}">
                        <Button Content="Button 1" MinWidth="80" />
                        <Button Content="Button 2" MinWidth="100" />
                        <Button Content="Button 3" MinWidth="90" />
                        <Button Content="Button 4" MinWidth="110" />
                        <Button Content="Button 5" MinWidth="85" />
                        <Button Content="Button 6" MinWidth="95" />
                        <Button Content="Button 7" MinWidth="120" />
                        <Button Content="Button 8" MinWidth="75" />
                    </husk:FlexWrapPanel>
                </Border>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Responsive Card Layout"
                                       Description="Cards that adapt to available width">
                <Border BorderBrush="{StaticResource ControlBorderBrush}" 
                        BorderThickness="1" 
                        CornerRadius="8" 
                        Padding="16"
                        Width="600">
                    <husk:FlexWrapPanel ColumnSpacing="16" RowSpacing="16">
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Analytics" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="View reports" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                        
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Users" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="Manage users" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                        
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="⚙️" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Settings" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="Configure app" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                        
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Files" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="Browse files" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                        
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="🔔" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Notifications" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="View alerts" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                        
                        <husk:Card MinWidth="150" MaxWidth="200" Padding="16">
                            <StackPanel Spacing="8">
                                <TextBlock Text="🎨" FontSize="24" HorizontalAlignment="Center" />
                                <TextBlock Text="Themes" FontWeight="Bold" HorizontalAlignment="Center" />
                                <TextBlock Text="Customize UI" FontSize="12" HorizontalAlignment="Center" />
                            </StackPanel>
                        </husk:Card>
                    </husk:FlexWrapPanel>
                </Border>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Tag Collection"
                                       Description="Flexible tag layout with different sizes">
                <Border BorderBrush="{StaticResource ControlBorderBrush}" 
                        BorderThickness="1" 
                        CornerRadius="8" 
                        Padding="16"
                        Width="450">
                    <husk:FlexWrapPanel ColumnSpacing="8" RowSpacing="8">
                        <husk:Tag Content="C#" Classes="Primary" MinWidth="40" />
                        <husk:Tag Content="Avalonia UI" Classes="Success" MinWidth="60" />
                        <husk:Tag Content="MVVM" Classes="Warning" MinWidth="45" />
                        <husk:Tag Content="Cross-platform" MinWidth="80" />
                        <husk:Tag Content="Desktop" Classes="Primary" MinWidth="50" />
                        <husk:Tag Content="Open Source" Classes="Success" MinWidth="70" />
                        <husk:Tag Content="UI Framework" MinWidth="75" />
                        <husk:Tag Content="XAML" Classes="Warning" MinWidth="40" />
                        <husk:Tag Content="Reactive" Classes="Primary" MinWidth="55" />
                        <husk:Tag Content="Modern" Classes="Success" MinWidth="50" />
                        <husk:Tag Content="Fast" Classes="Danger" MinWidth="35" />
                        <husk:Tag Content="Flexible" MinWidth="55" />
                    </husk:FlexWrapPanel>
                </Border>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Form Layout"
                                       Description="Responsive form fields using FlexWrapPanel">
                <husk:Card Padding="24" Width="500">
                    <StackPanel Spacing="20">
                        <TextBlock Text="Contact Information" FontSize="18" FontWeight="Bold" />
                        
                        <husk:FlexWrapPanel ColumnSpacing="16" RowSpacing="16">
                            <StackPanel Spacing="4" MinWidth="120">
                                <TextBlock Text="First Name" FontSize="12" />
                                <TextBox Watermark="Enter first name" />
                            </StackPanel>
                            
                            <StackPanel Spacing="4" MinWidth="120">
                                <TextBlock Text="Last Name" FontSize="12" />
                                <TextBox Watermark="Enter last name" />
                            </StackPanel>
                            
                            <StackPanel Spacing="4" MinWidth="200">
                                <TextBlock Text="Email Address" FontSize="12" />
                                <TextBox Watermark="Enter email address" />
                            </StackPanel>
                            
                            <StackPanel Spacing="4" MinWidth="120">
                                <TextBlock Text="Phone" FontSize="12" />
                                <TextBox Watermark="Enter phone number" />
                            </StackPanel>
                            
                            <StackPanel Spacing="4" MinWidth="150">
                                <TextBlock Text="Country" FontSize="12" />
                                <ComboBox PlaceholderText="Select country">
                                    <ComboBoxItem Content="United States" />
                                    <ComboBoxItem Content="Canada" />
                                    <ComboBoxItem Content="United Kingdom" />
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Spacing="4" MinWidth="100">
                                <TextBlock Text="Zip Code" FontSize="12" />
                                <TextBox Watermark="Enter zip code" />
                            </StackPanel>
                        </husk:FlexWrapPanel>
                        
                        <husk:Divider />
                        
                        <StackPanel Orientation="Horizontal" Spacing="12" HorizontalAlignment="Right">
                            <Button Content="Cancel" />
                            <Button Content="Save Contact" Classes="Primary" />
                        </StackPanel>
                    </StackPanel>
                </husk:Card>
            </controls:ExampleContainer>

        </StackPanel>
    </ScrollViewer>

</UserControl>
