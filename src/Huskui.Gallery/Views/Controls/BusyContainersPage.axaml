<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:controls="clr-namespace:Huskui.Gallery.Controls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Huskui.Gallery.Views.Controls.BusyContainersPage">

    <ScrollViewer>
        <StackPanel Margin="24" Spacing="32">

            <controls:ExampleContainer Title="Basic Busy Container"
                                       Description="Container that shows loading state with blur effect">
                <Grid ColumnDefinitions="*,16,*" RowDefinitions="Auto,16,Auto">
                    <StackPanel Grid.Column="0" Grid.Row="0" Spacing="8">
                        <TextBlock Text="Normal State" FontWeight="Bold" />
                        <husk:BusyContainer IsBusy="False" Height="120" Width="200">
                            <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                    CornerRadius="8" Padding="16">
                                <StackPanel Spacing="8">
                                    <TextBlock Text="Content is visible" FontWeight="Bold" />
                                    <TextBlock Text="All interactions are enabled" />
                                    <Button Content="Click me" />
                                </StackPanel>
                            </Border>
                        </husk:BusyContainer>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Grid.Row="0" Spacing="8">
                        <TextBlock Text="Busy State" FontWeight="Bold" />
                        <husk:BusyContainer IsBusy="True" Height="120" Width="200">
                            <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                    CornerRadius="8" Padding="16">
                                <StackPanel Spacing="8">
                                    <TextBlock Text="Content is blurred" FontWeight="Bold" />
                                    <TextBlock Text="Interactions are blocked" />
                                    <Button Content="Can't click" />
                                </StackPanel>
                            </Border>
                        </husk:BusyContainer>
                    </StackPanel>
                </Grid>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Custom Pending Content"
                                       Description="Showing custom loading indicators">
                <Grid ColumnDefinitions="*,16,*" RowDefinitions="Auto,16,Auto">
                    <StackPanel Grid.Column="0" Grid.Row="0" Spacing="8">
                        <TextBlock Text="Default Loading" FontWeight="Bold" />
                        <husk:BusyContainer IsBusy="True" Height="120" Width="200">
                            <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                    CornerRadius="8" Padding="16">
                                <StackPanel Spacing="8">
                                    <TextBlock Text="Loading data..." />
                                    <ProgressBar IsIndeterminate="True" />
                                </StackPanel>
                            </Border>
                        </husk:BusyContainer>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Grid.Row="0" Spacing="8">
                        <TextBlock Text="Custom Loading" FontWeight="Bold" />
                        <husk:BusyContainer IsBusy="True" Height="120" Width="200">
                            <husk:BusyContainer.PendingContent>
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="8">
                                    <ProgressBar IsIndeterminate="True" Width="100" Height="4" />
                                    <TextBlock Text="Processing..." HorizontalAlignment="Center" />
                                </StackPanel>
                            </husk:BusyContainer.PendingContent>
                            <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                                    CornerRadius="8" Padding="16">
                                <StackPanel Spacing="8">
                                    <TextBlock Text="Content behind" />
                                    <Button Content="Hidden button" />
                                </StackPanel>
                            </Border>
                        </husk:BusyContainer>
                    </StackPanel>
                </Grid>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Form Example"
                                       Description="Common use case for form submission">
                <husk:BusyContainer x:Name="FormContainer" IsBusy="False" Width="300">
                    <husk:BusyContainer.PendingContent>
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="12">
                            <ProgressBar IsIndeterminate="True" Width="120" Height="4" />
                            <TextBlock Text="Submitting form..." HorizontalAlignment="Center" FontWeight="Bold" />
                        </StackPanel>
                    </husk:BusyContainer.PendingContent>
                    
                    <husk:Card Padding="24">
                        <StackPanel Spacing="16">
                            <TextBlock Text="User Registration" FontSize="18" FontWeight="Bold" />
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Name" />
                                <TextBox Watermark="Enter your name" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Email" />
                                <TextBox Watermark="Enter your email" />
                            </StackPanel>
                            
                            <StackPanel Spacing="8">
                                <TextBlock Text="Password" />
                                <TextBox Watermark="Enter password" PasswordChar="*" />
                            </StackPanel>
                            
                            <Button Content="Submit" Classes="Primary" Click="OnSubmitClick" />
                        </StackPanel>
                    </husk:Card>
                </husk:BusyContainer>
            </controls:ExampleContainer>

            <controls:ExampleContainer Title="Interactive Demo"
                                       Description="Toggle busy state to see the effect">
                <controls:ExampleContainer.Controls>
                    <StackPanel Spacing="12">
                        <TextBlock Text="Controls" FontWeight="Bold" FontSize="14" />
                        <ToggleButton x:Name="BusyToggle"
                                      Content="Toggle Busy"
                                      IsChecked="{Binding #DemoContainer.IsBusy}" />
                        <TextBlock Text="Toggle the busy state to see the blur effect and loading indicator."
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </controls:ExampleContainer.Controls>

                <husk:BusyContainer x:Name="DemoContainer" IsBusy="False" Height="150" Width="300">
                    <husk:BusyContainer.PendingContent>
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="8">
                            <ProgressBar IsIndeterminate="True" Width="100" Height="4" />
                            <TextBlock Text="Loading content..." HorizontalAlignment="Center" />
                        </StackPanel>
                    </husk:BusyContainer.PendingContent>

                    <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                            CornerRadius="8" Padding="20">
                        <StackPanel Spacing="12">
                            <TextBlock Text="Interactive Content" FontSize="16" FontWeight="Bold" />
                            <TextBlock Text="This content will be blurred when busy state is active." />
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <Button Content="Button 1" />
                                <Button Content="Button 2" />
                                <Button Content="Button 3" />
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </husk:BusyContainer>
            </controls:ExampleContainer>

        </StackPanel>
    </ScrollViewer>

</UserControl>
