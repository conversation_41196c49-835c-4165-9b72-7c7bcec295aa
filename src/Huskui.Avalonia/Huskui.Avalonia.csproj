<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <LangVersion>preview</LangVersion>

        <!-- NuGet Package Metadata -->
        <PackageId>Huskui.Avalonia</PackageId>
        <Authors>d3ara1n</Authors>
        <Company>d3ara1n</Company>
        <Product>Huskui.Avalonia</Product>
        <Description>A modern, elegant UI component library for Avalonia UI, providing a comprehensive set of customizable controls for building beautiful cross-platform desktop applications.</Description>
        <PackageTags>avalonia;ui;controls;xaml;cross-platform;desktop</PackageTags>
        <PackageProjectUrl>https://github.com/d3ara1n/Huskui.Avalonia</PackageProjectUrl>
        <RepositoryUrl>https://github.com/d3ara1n/Huskui.Avalonia</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageLicenseExpression>MIT</PackageLicenseExpression>
        <PackageReadmeFile>README.md</PackageReadmeFile>
        <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
        <PublishRepositoryUrl>true</PublishRepositoryUrl>
        <EmbedUntrackedSources>true</EmbedUntrackedSources>
        <IncludeSymbols>true</IncludeSymbols>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
        <Title>Huskui for Avalonia</Title>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="[11.3.0,)"/>
        <PackageReference Include="FluentIcons.Avalonia" Version="1.1.303"/>
    </ItemGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <!-- Include files needed for NuGet packaging -->
    <ItemGroup>
        <None Include="Assets\icon.png" Pack="true" PackagePath="\" Condition="Exists('Assets\icon.png')"/>
        <None Include="..\..\README.md" Pack="true" PackagePath="\"/>
    </ItemGroup>

    <!-- Source Link for better debugging experience -->
    <ItemGroup>
        <PackageReference Include="GitVersion.MsBuild" Version="6.4.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="JetBrains.Annotations" Version="2025.2.0"/>
        <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
    </ItemGroup>
</Project>
