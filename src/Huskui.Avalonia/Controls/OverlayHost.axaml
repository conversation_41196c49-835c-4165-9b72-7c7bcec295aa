<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">

    <Design.PreviewWith>
        <Panel Background="White" Width="800" Height="600">
            <local:OverlayHost>
                <local:OverlayItem Background="Yellow" />
                <local:OverlayItem Background="Brown" />
                <local:OverlayItem Background="Chartreuse" />
                <local:OverlayItem Background="DeepSkyBlue" />
                <local:OverlayItem Background="Olive">
                    <Panel>
                        <Border BorderThickness="4" BorderBrush="Coral" />
                        <StackPanel>
                            <TextBlock Text="Hello" />
                            <Button Content="World" />
                        </StackPanel>
                    </Panel>
                </local:OverlayItem>
            </local:OverlayHost>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type local:OverlayHost}" TargetType="local:OverlayHost">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <Panel />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border Name="Smoke" Opacity="0"
                            IsHitTestVisible="False"
                            Background="{StaticResource OverlaySmokeBackgroundBrush}">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition
                                    Easing="CubicEaseOut"
                                    Property="Opacity"
                                    Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ItemsPresenter Name="{x:Static local:OverlayHost.PART_ItemsPresenter}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    Margin="{TemplateBinding Padding}" ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:present /template/ Border#Smoke">
            <Setter Property="Opacity" Value="1" />
            <Setter Property="IsHitTestVisible" Value="True" />
        </Style>
    </ControlTheme>
</ResourceDictionary>