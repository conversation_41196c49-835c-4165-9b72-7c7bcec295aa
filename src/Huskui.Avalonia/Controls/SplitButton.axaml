<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="using:FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24">
                <SplitButton>
                    <TextBlock Text="Hello World" />

                    <SplitButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="Menu" />
                        </MenuFlyout>
                    </SplitButton.Flyout>
                </SplitButton>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type SplitButton}" TargetType="SplitButton">
        <Setter Property="Background" Value="{StaticResource ControlInteractiveBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="KeyboardNavigation.IsTabStop" Value="True" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="Padding" Value="14,9" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="2">
                    <Button x:Name="PART_PrimaryButton" Grid.Column="0"
                            Foreground="{TemplateBinding Foreground}"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness, Converter={x:Static local:ThicknessConverters.WithoutRight}}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Content="{TemplateBinding Content}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            Command="{TemplateBinding Command}"
                            CommandParameter="{TemplateBinding CommandParameter}"
                            CornerRadius="{TemplateBinding CornerRadius, Converter={x:Static local:CornerRadiusConverters.Left}}"
                            FontFamily="{TemplateBinding FontFamily}"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                            Padding="{TemplateBinding Padding}"
                            Focusable="False"
                            KeyboardNavigation.IsTabStop="False" />
                    <Button x:Name="PART_SecondaryButton" Grid.Column="1"
                            Foreground="{TemplateBinding Foreground}"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness, Converter={x:Static local:ThicknessConverters.WithoutLeft}}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            CornerRadius="{TemplateBinding CornerRadius, Converter={x:Static local:CornerRadiusConverters.Right}}"
                            Padding="4,0"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Focusable="False"
                            KeyboardNavigation.IsTabStop="False">
                        <fi:SymbolIcon Width="{TemplateBinding FontSize}"
                                       Symbol="ChevronDown" Foreground="{TemplateBinding Foreground}" />
                    </Button>
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:flyout-open /template/ Button">
            <Setter Property="Tag" Value="flyout-open" />
        </Style>

        <Style Selector="^:checked /template/ Button">
            <Setter Property="Tag" Value="checked" />
        </Style>

        <Style Selector="^:checked:flyout-open /template/ Button">
            <Setter Property="Tag" Value="checked-flyout-open" />
        </Style>
    </ControlTheme>
</ResourceDictionary>