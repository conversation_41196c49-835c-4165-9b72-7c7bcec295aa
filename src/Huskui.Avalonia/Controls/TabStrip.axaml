<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Design.PreviewWith>
        <Panel>
            <StackPanel Margin="24" Spacing="12">
                <TabStrip>
                    <TabStripItem>
                        <TextBlock>
                            <Run Text="Hub" FontSize="{StaticResource LargeFontSize}" />
                            <LineBreak />
                            <Run Text="Design Center" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </TextBlock>
                    </TabStripItem>
                    <TabStripItem IsEnabled="False">
                        <TextBlock>
                            <Run Text="Dock" FontSize="{StaticResource LargeFontSize}" />
                            <LineBreak />
                            <Run Text="Machine Assembler"
                                 Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </TextBlock>
                    </TabStripItem>
                    <TabStripItem>
                        <TextBlock>
                            <Run Text="Socket" FontSize="{StaticResource LargeFontSize}" />
                            <LineBreak />
                            <Run Text="Power Supply" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </TextBlock>
                    </TabStripItem>
                </TabStrip>
                <TabStrip Theme="{StaticResource SegmentedTabStripTheme}">
                    <TabStripItem Content="Monday" />
                    <TabStripItem Content="Tuesday" />
                    <TabStripItem Content="Wednesday" />
                    <TabStripItem Content="Thursday" />
                    <TabStripItem Content="Friday" />
                </TabStrip>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type TabStrip}" TargetType="TabStrip">
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <StackPanel Spacing="3" Orientation="Horizontal" />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <ItemsPresenter Name="PART_ItemsPresenter"
                                    ItemsPanel="{TemplateBinding ItemsPanel}" Margin="{TemplateBinding Padding}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="SegmentedTabStripTheme" TargetType="TabStrip">
        <Setter Property="Background" Value="{StaticResource OverlayBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="ItemContainerTheme" Value="{StaticResource SegmentedTabStripItemTheme}" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <StackPanel Spacing="4" Orientation="Horizontal" />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Padding="{TemplateBinding Padding}" CornerRadius="{TemplateBinding CornerRadius}"
                        Background="{TemplateBinding Background}"
                        BorderThickness="{TemplateBinding BorderThickness}" BorderBrush="{TemplateBinding BorderBrush}">
                    <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="PivotTabStripTheme" TargetType="TabStrip">
        <Setter Property="ItemContainerTheme" Value="{StaticResource PivotTabStripItemTheme}" />
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <StackPanel Orientation="Horizontal" />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Padding="{TemplateBinding Padding}" CornerRadius="{TemplateBinding CornerRadius}"
                        Background="{TemplateBinding Background}"
                        BorderThickness="{TemplateBinding BorderThickness}" BorderBrush="{TemplateBinding BorderBrush}">
                    <ItemsPresenter ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type TabStripItem}" TargetType="TabStripItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="10,8" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="{StaticResource OverlayInteractiveBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border x:Name="Border" Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush"
                                                 Duration="{StaticResource ControlNormalAnimationDuration}"
                                                 Easing="SineEaseOut" />
                                <ThicknessTransition Property="BorderThickness"
                                                     Duration="{StaticResource ControlNormalAnimationDuration}"
                                                     Easing="SineEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                        <Border Name="Indicator" Background="{StaticResource OverlayHalfBackgroundBrush}" Opacity="0"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity"
                                                      Duration="{StaticResource ControlNormalAnimationDuration}"
                                                      Easing="SineEaseOut" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                    </Border>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Padding="{TemplateBinding Padding}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Content="{TemplateBinding Content}"
                        FontFamily="{TemplateBinding FontFamily}"
                        FontWeight="{TemplateBinding FontWeight}"
                        Foreground="{TemplateBinding Foreground}"
                        FontSize="{TemplateBinding FontSize}"
                        FontStretch="{TemplateBinding FontStretch}"
                        FontStyle="{TemplateBinding FontStyle}"
                        ContentTemplate="{TemplateBinding ContentTemplate}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        RecognizesAccessKey="True"
                        TextElement.FontSize="{TemplateBinding FontSize}"
                        TextElement.FontWeight="{TemplateBinding FontWeight}" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover:not(:selected) /template/ Border#Indicator">
            <Setter Property="Opacity" Value="1" />
        </Style>

        <Style Selector="^:selected /template/ Border#Border">
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            <Setter Property="BorderThickness" Value="2" />
        </Style>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="SegmentedTabStripItemTheme" TargetType="TabStripItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="8,6" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Background="{StaticResource TransparentBrush}">
                    <Border Name="Indicator" Background="{TemplateBinding Background}" Opacity="0"
                            RenderTransform="scale(0.95)"
                            ClipToBounds="{TemplateBinding ClipToBounds}"
                            BorderBrush="{TemplateBinding BorderBrush}" CornerRadius="{TemplateBinding CornerRadius}"
                            BorderThickness="{TemplateBinding BorderThickness}" BoxShadow="0 2 8 0 #1F000000">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="BackEaseOut"
                                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter" Content="{TemplateBinding Content}"
                                      Margin="{TemplateBinding Padding}"
                                      Foreground="{TemplateBinding Foreground}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}">
                        <ContentPresenter.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </ContentPresenter.Transitions>
                    </ContentPresenter>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover:not(:selected) /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource Overlay3Opacity}" />
        </Style>

        <Style Selector="^:selected /template/ Border#Indicator">
            <Setter Property="Opacity" Value="1.0" />
            <Setter Property="RenderTransform" Value="scale(1.0)" />
        </Style>

        <Style Selector="^:not(:selected) /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{StaticResource ControlSecondaryForegroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="PivotTabStripItemTheme" TargetType="TabStripItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="14,6" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource ExtraSmallCornerRadius}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container" Background="{TemplateBinding Background}">
                    <Panel.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </Panel.Transitions>
                    <Border x:Name="PART_Indicator" Background="{StaticResource ControlBackgroundBrush}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="Background" Easing="SineEaseIn"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="QuarticEaseInOut"
                                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Margin="{TemplateBinding Padding}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Content="{TemplateBinding Content}"
                        ContentTemplate="{TemplateBinding ContentTemplate}" />
                </Panel>
            </ControlTemplate>
        </Setter>


        <Style Selector="^:pointerover:not(:selected) /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource Overlay3Opacity}" />
        </Style>
        <Style Selector="^:selected /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^ /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Bottom" />
            <Setter Property="Height"
                    Value="{StaticResource TabItemHighlightThickness}" />
            <Setter Property="RenderTransform" Value="scaleX(0.0)" />
        </Style>

        <Style Selector="^:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(0.7)" />
        </Style>
        <Style Selector="^:pointerover /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>

    </ControlTheme>
</ResourceDictionary>