<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="using:System"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Grid Width="1024" Height="340" ColumnDefinitions="*,*">
            <TabControl Grid.Column="0" Margin="24" TabStripPlacement="Top">
                <TabItem>
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <fi:SymbolIcon Symbol="Info" FontSize="{StaticResource MediumFontSize}" />
                            <TextBlock Text="Fancy" />
                        </StackPanel>
                    </TabItem.Header>
                    <Button Content="Too" />
                </TabItem>
                <TabItem Header="Too" Classes="Primary">
                    <Button Content="Fancy" />
                </TabItem>
                <TabItem Header="Disabled" IsSelected="True" Content="The Whole World" IsEnabled="False" />
                <TabItem Header="Hello" Theme="{StaticResource SolidTabItemTheme}">
                    <Button Content="Peanut" Theme="{StaticResource GhostButtonTheme}" Classes="Primary"
                            HorizontalAlignment="Center" VerticalAlignment="Center" />
                </TabItem>
                <TabItem Header="Primary" Classes="Primary" Theme="{StaticResource SolidTabItemTheme}">
                    <Button Content="Butter" Classes="Primary" />
                </TabItem>
            </TabControl>
            <TabControl Grid.Column="1" Margin="24" TabStripPlacement="Left">
                <TabItem Header="Fancy">
                    <Button Content="Too" />
                </TabItem>
                <TabItem Header="Too" Classes="Primary">
                    <Button Content="Fancy" />
                </TabItem>
                <TabItem Header="Disabled" IsSelected="True" Content="The Whole World" IsEnabled="False" />
                <TabItem Header="Hello" Theme="{StaticResource SolidTabItemTheme}">
                    <Button Content="Peanut" Background="{StaticResource TransparentBrush}" />
                </TabItem>
                <TabItem Header="Primary" Classes="Primary" Theme="{StaticResource SolidTabItemTheme}">
                    <Button Content="Butter" Classes="Primary" />
                </TabItem>
            </TabControl>
        </Grid>
    </Design.PreviewWith>

    <Thickness x:Key="TabContentMargin">12</Thickness>

    <sys:Double x:Key="TabItemHighlightThickness">4</sys:Double>
    <sys:Double x:Key="TabItemHighlightMidPoint">0.4</sys:Double>

    <ControlTheme x:Key="{x:Type TabControl}" TargetType="TabControl">
        <Setter Property="Padding" Value="{StaticResource TabContentMargin}" />
        <Setter Property="Template">
            <ControlTemplate TargetType="TabControl">
                <Border>
                    <DockPanel>
                        <Panel DockPanel.Dock="{TemplateBinding TabStripPlacement}">
                            <ScrollViewer
                                HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                                <ItemsPresenter Name="PART_ItemsPresenter"
                                                ItemsPanel="{TemplateBinding ItemsPanel}" />
                            </ScrollViewer>
                        </Panel>
                        <!-- <ContentPresenter -->
                        <!--     Name="PART_ContentPresenter" -->
                        <!--     Padding="{TemplateBinding Padding}" -->
                        <!--     Background="{TemplateBinding Background}" -->
                        <!--     BackgroundSizing="{TemplateBinding BackgroundSizing}" -->
                        <!--     BorderBrush="{TemplateBinding BorderBrush}" -->
                        <!--     BorderThickness="{TemplateBinding BorderThickness}" -->
                        <!--     CornerRadius="{TemplateBinding CornerRadius}" -->
                        <!--     HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" -->
                        <!--     VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" -->
                        <!--     Content="{TemplateBinding SelectedContent}" -->
                        <!--     ContentTemplate="{TemplateBinding SelectedContentTemplate}" /> -->
                        <TransitioningContentControl
                            Name="PART_ContentPresenter"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BackgroundSizing="{TemplateBinding BackgroundSizing}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                            Content="{TemplateBinding SelectedContent}"
                            ContentTemplate="{TemplateBinding SelectedContentTemplate}" />
                    </DockPanel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^[TabStripPlacement=Left] /template/ ItemsPresenter#PART_ItemsPresenter > WrapPanel">
            <Setter Property="Orientation" Value="Vertical" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right] /template/ ItemsPresenter#PART_ItemsPresenter > WrapPanel">
            <Setter Property="Orientation" Value="Vertical" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="NeoTabControlTheme" TargetType="TabControl" BasedOn="{StaticResource {x:Type TabControl}}">
        <Setter Property="ItemContainerTheme" Value="{StaticResource NeoTabItemTheme}" />
    </ControlTheme>

    <ControlTheme x:Key="SolidTabControlTheme" TargetType="TabControl" BasedOn="{StaticResource {x:Type TabControl}}">
        <Setter Property="ItemContainerTheme" Value="{StaticResource SolidTabItemTheme}" />
    </ControlTheme>

    <ControlTheme x:Key="{x:Type TabItem}" TargetType="TabItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="14,6" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource ExtraSmallCornerRadius}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container" Background="{TemplateBinding Background}">
                    <Panel.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </Panel.Transitions>
                    <Border x:Name="PART_Indicator" Background="{StaticResource ControlBackgroundBrush}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="Background" Easing="SineEaseIn"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="QuarticEaseInOut"
                                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Margin="{TemplateBinding Padding}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Content="{TemplateBinding Header}"
                        FontFamily="{TemplateBinding FontFamily}"
                        FontWeight="{TemplateBinding FontWeight}"
                        Foreground="{TemplateBinding Foreground}"
                        FontSize="{TemplateBinding FontSize}"
                        FontStretch="{TemplateBinding FontStretch}"
                        FontStyle="{TemplateBinding FontStyle}"
                        ContentTemplate="{TemplateBinding HeaderTemplate}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        RecognizesAccessKey="True"
                        TextElement.FontSize="{TemplateBinding FontSize}"
                        TextElement.FontWeight="{TemplateBinding FontWeight}" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover:not(:selected) /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource Overlay3Opacity}" />
        </Style>
        <Style Selector="^:selected /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^[TabStripPlacement=Top] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Bottom" />
            <Setter Property="Height"
                    Value="{StaticResource TabItemHighlightThickness}" />
            <Setter Property="RenderTransform" Value="scaleX(0.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Top]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(0.7)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Top]:pointerover /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Top" />
            <Setter Property="Height"
                    Value="{StaticResource TabItemHighlightThickness}" />
            <Setter Property="RenderTransform" Value="scaleX(0.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(0.7)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:pointerover /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="Width"
                    Value="{StaticResource TabItemHighlightThickness}" />
            <Setter Property="RenderTransform" Value="scaleY(0.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleY(0.7)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:pointerover /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="Width"
                    Value="{StaticResource TabItemHighlightThickness}" />
            <Setter Property="RenderTransform" Value="scaleY(0.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleY(0.7)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:pointerover /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="NeoTabItemTheme" TargetType="TabItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="10,6" />
        <Setter Property="Margin" Value="2" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Background="{TemplateBinding Background}">
                    <Border Name="PART_Overlay" Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                            CornerRadius="{TemplateBinding CornerRadius}" Opacity="0.65">
                        <Border.OpacityMask>
                            <SolidColorBrush Color="{StaticResource TransparentColor}" />
                        </Border.OpacityMask>
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="OpacityMask"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseIn" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <Border Name="PART_Indicator"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Background="{StaticResource ControlAccentInteractiveBorderBrush}">
                        <Border.Transitions>
                            <Transitions>
                                <TransformOperationsTransition Property="RenderTransform"
                                                               Duration="{StaticResource ControlFastestAnimationDuration}"
                                                               Easing="SineEaseIn" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter Name="PART_ContentPresenter" Content="{TemplateBinding Header}"
                                      ContentTemplate="{TemplateBinding HeaderTemplate}"
                                      Padding="{TemplateBinding Padding}" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Border#PART_Indicator">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ Border#PART_Overlay">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^[TabStripPlacement=Top] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Bottom" />
            <Setter Property="RenderTransformOrigin" Value="50%,100%" />
            <Setter Property="RenderTransform" Value="scaleX(0.0)" />
            <Setter Property="Height" Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Top" />
            <Setter Property="RenderTransformOrigin" Value="50%,0%" />
            <Setter Property="RenderTransform" Value="scaleX(0.0)" />
            <Setter Property="Height" Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="RenderTransformOrigin" Value="0%,50%" />
            <Setter Property="RenderTransform" Value="scaleY(0.0)" />
            <Setter Property="Width" Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="RenderTransformOrigin" Value="100%,50%" />
            <Setter Property="RenderTransform" Value="scaleY(0.0)" />
            <Setter Property="Width" Value="{StaticResource TabItemHighlightThickness}" />
        </Style>

        <Style Selector="^[TabStripPlacement=Top]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleY(1.0)" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:selected /template/ Border#PART_Indicator">
            <Setter Property="RenderTransform" Value="scaleY(1.0)" />
        </Style>


        <Style Selector="^[TabStripPlacement=Top]:selected /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="50%,0%" EndPoint="50%,100%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}"
                                      Offset="{StaticResource TabItemHighlightMidPoint}" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:selected /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="50%,100%" EndPoint="50%,0%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}"
                                      Offset="{StaticResource TabItemHighlightMidPoint}" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:selected /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="100%,50%" EndPoint="0%,50%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}"
                                      Offset="{StaticResource TabItemHighlightMidPoint}" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:selected /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="0%,50%" EndPoint="100%,50%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}"
                                      Offset="{StaticResource TabItemHighlightMidPoint}" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>

        <Style Selector="^[TabStripPlacement=Top]:pointerover /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="50%,0%" EndPoint="50%,100%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}" Offset="0" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:pointerover /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="50%,100%" EndPoint="50%,0%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}" Offset="0" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:pointerover /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="100%,50%" EndPoint="0%,50%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}" Offset="0" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:pointerover /template/ Border#PART_Overlay">
            <Setter Property="OpacityMask">
                <LinearGradientBrush StartPoint="0%,50%" EndPoint="100%,50%">
                    <GradientStops>
                        <GradientStop Color="{StaticResource TransparentColor}" Offset="0" />
                        <GradientStop Color="Black" Offset="1" />
                    </GradientStops>
                </LinearGradientBrush>
            </Setter>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="SolidTabItemTheme" TargetType="TabItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="14,6" />
        <Setter Property="Margin" Value="4" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Background="{TemplateBinding Background}">
                    <Border x:Name="PART_Indicator" Background="{StaticResource TransparentBrush}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background"
                                                 Duration="{StaticResource ControlNormalAnimationDuration}"
                                                 Easing="SineEaseOut" />
                                <DoubleTransition Property="Height" Easing="QuarticEaseInOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <DoubleTransition Property="Width" Easing="QuarticEaseInOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Padding="{TemplateBinding Padding}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Content="{TemplateBinding Header}"
                        FontFamily="{TemplateBinding FontFamily}"
                        FontWeight="{TemplateBinding FontWeight}"
                        Foreground="{TemplateBinding Foreground}"
                        FontSize="{TemplateBinding FontSize}"
                        FontStretch="{TemplateBinding FontStretch}"
                        FontStyle="{TemplateBinding FontStyle}"
                        ContentTemplate="{TemplateBinding HeaderTemplate}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        RecognizesAccessKey="True"
                        TextElement.FontSize="{TemplateBinding FontSize}"
                        TextElement.FontWeight="{TemplateBinding FontWeight}">
                        <ContentPresenter.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlNormalAnimationDuration}" />
                            </Transitions>
                        </ContentPresenter.Transitions>
                    </ContentPresenter>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:selected /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Border#PART_Indicator">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^:not(:selected):not(:pointerover) /template/ Border#PART_Indicator">
            <Setter Property="IsVisible" Value="False" />
        </Style>

        <Style Selector="^[TabStripPlacement=Top] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Bottom" />
            <Setter Property="Height"
                    Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom] /template/ Border#PART_Indicator">
            <Setter Property="VerticalAlignment" Value="Top" />
            <Setter Property="Height"
                    Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="Width"
                    Value="{StaticResource TabItemHighlightThickness}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right] /template/ Border#PART_Indicator">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="Width"
                    Value="{StaticResource TabItemHighlightThickness}" />
        </Style>

        <Style Selector="^[TabStripPlacement=Top]:selected /template/ Border#PART_Indicator">
            <Setter Property="Height"
                    Value="{Binding Bounds.Height,RelativeSource={RelativeSource Mode=TemplatedParent}}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Bottom]:selected /template/ Border#PART_Indicator">
            <Setter Property="Height"
                    Value="{Binding Bounds.Height,RelativeSource={RelativeSource Mode=TemplatedParent}}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Left]:selected /template/ Border#PART_Indicator">
            <Setter Property="Width"
                    Value="{Binding Bounds.Width,RelativeSource={RelativeSource Mode=TemplatedParent}}" />
        </Style>
        <Style Selector="^[TabStripPlacement=Right]:selected /template/ Border#PART_Indicator">
            <Setter Property="Width"
                    Value="{Binding Bounds.Width,RelativeSource={RelativeSource Mode=TemplatedParent}}" />
        </Style>

        <Style Selector="^.Primary:pointerover /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
        </Style>
        <Style Selector="^.Primary:selected /template/ Border#PART_Indicator">
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>