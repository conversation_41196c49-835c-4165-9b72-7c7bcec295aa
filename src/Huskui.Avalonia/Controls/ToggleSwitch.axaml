<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Spacing="12">
                <ToggleSwitch Content="Label" />
                <ToggleSwitch IsChecked="True" />
                <ToggleSwitch IsChecked="False" IsEnabled="False" />
                <ToggleSwitch IsChecked="True" IsEnabled="False" />
                <StackPanel Orientation="Horizontal">
                    <Button />
                    <ToggleSwitch OffContent="{x:Null}" OnContent="{x:Null}" />
                    <Button />
                </StackPanel>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type ToggleSwitch}"
                  TargetType="ToggleSwitch">
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="CornerRadius" Value="{StaticResource FullCornerRadius}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="KnobTransitions">
            <Transitions>
                <DoubleTransition
                    Easing="CubicEaseOut"
                    Property="Canvas.Left"
                    Duration="{StaticResource ControlNormalAnimationDuration}" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Grid RowDefinitions="Auto,*">
                    <ContentPresenter Grid.Row="0" Name="PART_ContentPresenter"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      RecognizesAccessKey="True" />
                    <Grid Grid.Row="1" ColumnDefinitions="Auto,Auto">
                        <ContentPresenter Name="PART_OffContentPresenter"
                                          Grid.Column="1"
                                          Margin="6,0,0,0"
                                          IsVisible="{TemplateBinding OffContent,Converter={x:Static ObjectConverters.IsNotNull}}"
                                          Content="{TemplateBinding OffContent}"
                                          ContentTemplate="{TemplateBinding OffContentTemplate}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        <ContentPresenter Name="PART_OnContentPresenter"
                                          Grid.Column="1"
                                          Margin="6,0,0,0"
                                          IsVisible="{TemplateBinding OnContent,Converter={x:Static ObjectConverters.IsNotNull}}"
                                          Content="{TemplateBinding OnContent}"
                                          ContentTemplate="{TemplateBinding OnContentTemplate}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        <Border Grid.Column="0" Name="SwitchKnobBounds" Width="40" Height="20"
                                Cursor="Hand"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Background"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}" />
                                </Transitions>
                            </Border.Transitions>
                            <Canvas x:Name="PART_SwitchKnob"
                                    HorizontalAlignment="Left" Width="20"
                                    Height="20">
                                <Panel x:Name="PART_MovingKnobs" Width="20" Height="20">
                                    <Border Name="SwitchKnob" Width="16" Height="16"
                                            Background="{StaticResource OverlaySolidBackgroundBrush}"
                                            BoxShadow="2 2 4 0 #25000000"
                                            CornerRadius="{StaticResource FullCornerRadius}">
                                        <Border.Transitions>
                                            <Transitions>
                                                <TransformOperationsTransition Property="RenderTransform"
                                                                               Duration="{StaticResource ControlFastestAnimationDuration}" />
                                            </Transitions>
                                        </Border.Transitions>
                                    </Border>
                                </Panel>
                            </Canvas>
                        </Border>
                    </Grid>
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Border#SwitchKnobBounds">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^:pointerover /template/ Border#SwitchKnob">
            <Setter Property="RenderTransform" Value="scale(0.9)" />
        </Style>
        <Style Selector="^:pressed /template/ Border#SwitchKnob">
            <Setter Property="RenderTransform" Value="scale(0.8)" />
        </Style>

        <Style Selector="^:checked">
            <Style Selector="^ /template/ Border#SwitchKnobBounds">
                <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_OnContentPresenter">
                <Setter Property="Opacity" Value="1" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_OffContentPresenter">
                <Setter Property="Opacity" Value="0" />
            </Style>
        </Style>

        <Style Selector="^:unchecked">
            <Style Selector="^ /template/ ContentPresenter#PART_OnContentPresenter">
                <Setter Property="Opacity" Value="0" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_OffContentPresenter">
                <Setter Property="Opacity" Value="1" />
            </Style>
        </Style>
    </ControlTheme>
</ResourceDictionary>