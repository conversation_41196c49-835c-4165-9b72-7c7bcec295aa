<ResourceDictionary
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">

    <ControlTheme x:Key="{x:Type local:AppWindow}" TargetType="local:AppWindow">
        <Setter Property="Background" Value="{StaticResource WindowBackgroundBrush}" />
        <Setter Property="TransparencyBackgroundFallback" Value="{StaticResource WindowBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border Name="PART_TransparencyFallback" IsHitTestVisible="False" />
                    <ExperimentalAcrylicBorder Name="PART_AcrylicMaterial" IsHitTestVisible="False" IsVisible="False">
                        <ExperimentalAcrylicBorder.Material>
                            <ExperimentalAcrylicMaterial
                                BackgroundSource="Digger"
                                TintColor="{DynamicResource SolidColor}"
                                TintOpacity="1"
                                MaterialOpacity="0.65" />
                        </ExperimentalAcrylicBorder.Material>
                    </ExperimentalAcrylicBorder>
                    <Border Background="{TemplateBinding Background}" IsHitTestVisible="False">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition
                                    Easing="CubicEaseOut"
                                    Property="Background"
                                    Duration="{StaticResource ControlSlowestAnimationDuration}" />
                                <BrushTransition
                                    Easing="SineEaseOut"
                                    Property="BorderBrush"
                                    Duration="{StaticResource ControlSlowerAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <Panel Margin="{TemplateBinding WindowDecorationMargin}" />
                    <VisualLayerManager>
                        <VisualLayerManager.ChromeOverlayLayer>
                            <local:OverlayHost Name="{x:Static local:AppWindow.PART_ToastHost}" Padding="12,32,12,0">
                                <local:OverlayHost.Transition>
                                    <local:HookUpTransition />
                                </local:OverlayHost.Transition>
                            </local:OverlayHost>
                            <local:OverlayHost
                                Name="{x:Static local:AppWindow.PART_ModalHost}"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center">
                                <local:OverlayHost.Transition>
                                    <local:HookUpTransition />
                                </local:OverlayHost.Transition>
                            </local:OverlayHost>
                            <local:OverlayHost
                                Name="{x:Static local:AppWindow.PART_DialogHost}"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center">
                                <local:OverlayHost.Transition>
                                    <local:ScaleInTransition />
                                </local:OverlayHost.Transition>
                            </local:OverlayHost>
                            <local:NotificationHost
                                Name="{x:Static local:AppWindow.PART_NotificationHost}"
                                Margin="18,12"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Bottom"
                                HorizontalContentAlignment="Right">
                                <local:NotificationHost.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Spacing="2" />
                                    </ItemsPanelTemplate>
                                </local:NotificationHost.ItemsPanel>
                            </local:NotificationHost>
                        </VisualLayerManager.ChromeOverlayLayer>
                        <Panel Margin="{Binding $parent[Window].OffScreenMargin}">
                            <ContentPresenter
                                Name="ContentPresenter"
                                Margin="{TemplateBinding Padding}"
                                HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                Content="{TemplateBinding Content}"
                                ContentTemplate="{TemplateBinding ContentTemplate}">
                                <ContentPresenter.Transitions>
                                    <Transitions>
                                        <EffectTransition Property="Effect"
                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    </Transitions>
                                </ContentPresenter.Transitions>
                            </ContentPresenter>
                        </Panel>
                    </VisualLayerManager>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:obstructed /template/ ContentPresenter#ContentPresenter">
            <Setter Property="Effect">
                <BlurEffect />
            </Setter>
        </Style>

        <Style Selector="^local|AppWindow[ActualTransparencyLevel=Mica][IsActive=True]">
            <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        </Style>
        <Style Selector="^local|AppWindow[ActualTransparencyLevel=AcrylicBlur][IsActive=True]">
            <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        </Style>
        <Style Selector="^local|AppWindow[ActualTransparencyLevel=Blur][IsActive=True]">
            <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        </Style>

        <Style
            Selector="^local|AppWindow[ActualTransparencyLevel=AcrylicBlur] /template/ ExperimentalAcrylicBorder#PART_AcrylicMaterial">
            <Setter Property="IsVisible" Value="True" />
        </Style>
    </ControlTheme>
</ResourceDictionary>