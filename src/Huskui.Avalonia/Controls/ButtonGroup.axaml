<ResourceDictionary
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel>
            <StackPanel Margin="24" Spacing="12">
                <local:ButtonGroup>
                    <Button Content="访问" />
                    <Button>
                        <fi:SymbolIcon Symbol="Copy" FontSize="{StaticResource MediumFontSize}" />
                    </Button>
                </local:ButtonGroup>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:ButtonGroup}" TargetType="local:ButtonGroup">
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="ItemContainerTheme" Value="{StaticResource ButtonGroupItemTheme}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <StackPanel Orientation="Horizontal" />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ItemsPresenter Name="PART_ItemsPresenter" ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="ButtonGroupItemTheme" TargetType="Button">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Padding" Value="6,4" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container">
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Padding="{TemplateBinding Padding}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Background="{TemplateBinding Background}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Content="{TemplateBinding Content}"
                        ContentTemplate="{TemplateBinding ContentTemplate}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        RecognizesAccessKey="True"
                        TextElement.FontSize="{TemplateBinding FontSize}"
                        TextElement.FontWeight="{TemplateBinding FontWeight}" />
                    <Border
                        x:Name="Indicator"
                        Background="{StaticResource TransparentBrush}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition
                                    Easing="SineEaseOut"
                                    Property="Background"
                                    Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#Indicator">
            <Setter Property="Background" Value="{StaticResource OverlayHalfBackgroundBrush}" />
        </Style>

        <Style Selector="^:pressed /template/ Border#Indicator">
            <Setter Property="Background" Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>