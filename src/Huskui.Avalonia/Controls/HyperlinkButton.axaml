<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Width="256" Height="128">
            <StackPanel Margin="32" Spacing="8">
                <HyperlinkButton NavigateUri="https://github.com/d3ara1n/Huskui.Avalonia/">
                    Husk/ui
                </HyperlinkButton>
                <HyperlinkButton Classes="Dashed" NavigateUri="https://github.com/d3ara1n/Huskui.Avalonia/">
                    Husk/ui
                </HyperlinkButton>
                <HyperlinkButton NavigateUri="https://github.com/d3ara1n/Huskui.Avalonia/" IsEnabled="False">
                    哈斯可有爱
                </HyperlinkButton>
                <TextBlock>
                    <Run Text="Hello" />
                    <HyperlinkButton Content="World" />
                    <Run Text="!" />
                </TextBlock>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme TargetType="HyperlinkButton" x:Key="{x:Type HyperlinkButton}">
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container" Background="{TemplateBinding Background}">
                    <Panel.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFastestAnimationDuration}" />
                            <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseIn"
                                                           Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Panel.Transitions>
                    <Rectangle Name="Underline"
                               StrokeThickness="{TemplateBinding BorderThickness,Converter={x:Static husk:ThicknessConverters.ToDouble},ConverterParameter=bottom}"
                               Stroke="{TemplateBinding BorderBrush}" VerticalAlignment="Bottom"
                               HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                               Width="{Binding #PART_ContentPresenter.Bounds.Width}">
                        <Rectangle.Transitions>
                            <Transitions>
                                <BrushTransition Property="Stroke" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFastestAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                               Duration="{StaticResource ControlFastestAnimationDuration}" />
                            </Transitions>
                        </Rectangle.Transitions>
                    </Rectangle>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Content="{TemplateBinding Content}"
                        ContentTemplate="{TemplateBinding ContentTemplate}"
                        Foreground="{TemplateBinding Foreground}"
                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                        RecognizesAccessKey="True" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover/template/ Rectangle#Underline">
            <Setter Property="RenderTransform" Value="scaleY(2)" />
        </Style>

        <Style Selector="^:pressed/template/ Rectangle#Underline">
            <Setter Property="RenderTransform" Value="scaleY(0.0)" />
        </Style>

        <Style Selector="^:pressed /template/ Panel#Container">
            <Setter Property="Opacity" Value="0.7" />
            <Setter Property="RenderTransform" Value="translateY(2px)" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ Rectangle#Underline">
            <Setter Property="Stroke" Value="{StaticResource ControlBorderBrush}" />
        </Style>

        <Style Selector="^.Dashed /template/ Rectangle#Underline">
            <Setter Property="StrokeDashArray" Value="2,2" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="InlineHyperlinkButtonTheme" TargetType="HyperlinkButton">
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="BorderThickness" Value="0,0,0,2" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container" Background="{TemplateBinding Background}">
                    <Panel.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                              Duration="{StaticResource ControlFastestAnimationDuration}" />
                            <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseIn"
                                                           Duration="{StaticResource ControlFastestAnimationDuration}" />
                        </Transitions>
                    </Panel.Transitions>
                    <Rectangle Name="Underline"
                               StrokeThickness="{TemplateBinding BorderThickness,Converter={x:Static husk:ThicknessConverters.ToDouble},ConverterParameter=bottom}"
                               Stroke="{TemplateBinding BorderBrush}" VerticalAlignment="Bottom"
                               HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                               Width="{Binding #PART_ContentPresenter.Bounds.Width}"
                               RenderTransform="translateY(0px)">
                        <Rectangle.Transitions>
                            <Transitions>
                                <BrushTransition Property="Stroke" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFastestAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseIn"
                                                               Duration="{StaticResource ControlInstantAnimationDuration}" />
                            </Transitions>
                        </Rectangle.Transitions>
                    </Rectangle>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Content="{TemplateBinding Content}"
                        ContentTemplate="{TemplateBinding ContentTemplate}"
                        Foreground="{TemplateBinding Foreground}"
                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                        RecognizesAccessKey="True">
                        <ContentPresenter.Transitions>
                            <Transitions>
                                <BrushTransition Property="Foreground" Easing="SineEaseOut"
                                                 Duration="{StaticResource ControlFastestAnimationDuration}" />
                            </Transitions>
                        </ContentPresenter.Transitions>
                    </ContentPresenter>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover/template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{StaticResource ControlAccentForegroundBrush}" />
        </Style>
        <Style Selector="^:pointerover/template/ Rectangle#Underline">
            <Setter Property="RenderTransform" Value="translateY(-1px)" />
        </Style>

        <Style Selector="^:pressed /template/ Panel#Container">
            <Setter Property="Opacity" Value="0.7" />
            <Setter Property="RenderTransform" Value="translateY(3px)" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:disabled /template/ Rectangle#Underline">
            <Setter Property="Stroke" Value="{StaticResource ControlBorderBrush}" />
        </Style>
        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>